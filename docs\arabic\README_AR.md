# نظام إدارة المستودعات RAFCO - إصدار المتصفح

## نظرة عامة
هذا الدليل يوضح كيفية تشغيل واستخدام نظام إدارة المستودعات RAFCO عبر المتصفح. هذا الإصدار يعمل كخادم محلي ويمكن الوصول إليه عبر أي متصفح حديث.

## متطلبات النظام
- ويندوز 10/11 (64-بت)
- ذاكرة وصول عشوائي: 4 جيجابايت على الأقل (يُفضل 8 جيجابايت)
- مساحة القرص الصلب: 500 ميجابايت للتطبيق + مساحة إضافية للبيانات
- متصفح حديث: Chrome 90+، Firefox 88+، Edge 90+
- كاميرا (اختياري) لمسح الباركود/QR
- قارئات باركود USB (مدعومة بالكامل)

## تعليمات التحميل والتشغيل

### الطريقة الأولى: الملف التنفيذي المحمول
1. حمّل الملف `RAFCO-WMS-Server.exe` من فريق تقنية المعلومات
2. ضع الملف في مجلد مناسب (مثل سطح المكتب أو `C:\RAFCO WMS\`)
3. انقر نقراً مزدوجاً على الملف لتشغيله
4. انتظر حتى تظهر رسالة "Server running on http://localhost:3000"
5. سيفتح المتصفح تلقائياً، أو افتح المتصفح يدوياً واذهب إلى: `http://localhost:3000`

### الطريقة الثانية: من الكود المصدري (للمطورين)
```bash
npm install
npm run build:server
npm run start:server
```

## الاستخدام الأول

### إعداد اللغة
- عند فتح التطبيق لأول مرة، ستكون اللغة الافتراضية هي الإنجليزية
- انقر على زر "العربية" في أعلى الشاشة للتبديل إلى العربية
- التبديل فوري ولا يحتاج إعادة تحميل الصفحة

### فحص الاتصال
- تأكد من ظهور رسالة "متصل" في حالة الخادم
- اختبر قاعدة البيانات بالنقر على زر "اختبار قاعدة البيانات"

### أذونات المتصفح
- عند استخدام الكاميرا لأول مرة، سيطلب المتصفح إذن الوصول
- انقر "السماح" لتمكين مسح الباركود/QR
- لا تحتاج أذونات إضافية للعمليات الأخرى

## الوظائف الرئيسية

### إدارة المشاريع
- إنشاء وتتبع المشاريع من البداية حتى الانتهاء
- ربط المشاريع بأوامر الشراء والإصدارات والشحنات
- تتبع الميزانيات والتكاليف

### إدارة المخزون
- تسجيل المواد الخام والمستهلكات
- تتبع المخزون عبر المستودع الرئيسي والفروع (الدمام، الخضرية، الجبيل)
- مسح الباركود/QR للمواد
- تحديد مواقع التخزين الدقيقة

### المشتريات
- إنشاء أوامر شراء إلكترونية مرتبطة بالمشاريع
- إشعارات استلام البضائع الرقمية (GRN)
- فحص الجودة مع حالات القبول/الرفض
- رفع الصور والمستندات (فواتير، شهادات جودة)

### التصنيع والشحن
- تتبع استهلاك المواد في المصانع
- تسجيل الهدر حسب النوع والمصنع
- تتبع خاص للبراغي (حسب النوع/الوزن) والدهانات (حسب اللون/النوع)
- بوالص الشحن الرقمية مع الأوصاف والكميات والأوزان

### التقارير والتحليلات
- تقرير حالة المخزون مع التنبيهات
- تقرير مصروفات المشروع (مواد + مستهلكات)
- تقرير تحليل الهدر حسب المصنع
- توقع الطلب بناءً على البيانات التاريخية

## مسح الباركود/QR

### استخدام الكاميرا
1. انقر على أيقونة المسح في أي شاشة إدخال
2. اسمح للمتصفح بالوصول للكاميرا
3. وجّه الكاميرا نحو الباركود/QR
4. سيتم المسح تلقائياً عند التعرف على الكود

### استخدام قارئ USB
1. تأكد من توصيل قارئ الباركود بالكمبيوتر
2. ضع المؤشر في خانة الإدخال المطلوبة
3. امسح الباركود - سيظهر النص تلقائياً

## رفع الملفات والصور

### رفع الصور
1. انقر على زر "اختيار الملفات" أو "رفع صور"
2. اختر الصور من جهازك (JPG, PNG مدعومة)
3. انقر "رفع الملفات" لحفظها في النظام

### التقاط صور مباشرة
1. انقر على زر "تشغيل الكاميرا"
2. وجّه الكاميرا نحو الهدف
3. انقر "التقاط" لحفظ الصورة

## تصدير البيانات

### تصدير CSV
- انقر على زر "تصدير CSV" في أي تقرير
- سيتم تحميل الملف تلقائياً في مجلد التحميلات

### تصدير Excel
- انقر على زر "تصدير Excel" للحصول على ملف .xlsx
- يدعم النصوص العربية والتنسيق المناسب

### الطباعة
- استخدم وظيفة الطباعة في المتصفح (Ctrl+P)
- التخطيط يدعم النصوص العربية والاتجاه من اليمين لليسار

## مواقع تخزين البيانات

### قاعدة البيانات
```
%APPDATA%\RAFCO WMS\rafco-wms.sqlite3
```

### الملفات المرفوعة
```
%APPDATA%\RAFCO WMS\storage\
```

### النسخ الاحتياطي
1. أغلق التطبيق (أغلق المتصفح واضغط Ctrl+C في نافذة الخادم)
2. انسخ مجلد `%APPDATA%\RAFCO WMS\` بالكامل
3. احفظه في مكان آمن

### الاستعادة
1. أغلق التطبيق
2. استبدل مجلد `%APPDATA%\RAFCO WMS\` بالنسخة الاحتياطية
3. شغّل التطبيق مرة أخرى

## الفروقات بين النسخة المكتبية ونسخة المتصفح

### المزايا
- ✅ لا حاجة لتثبيت Electron (حجم أصغر)
- ✅ يمكن الوصول من أجهزة متعددة في الشبكة المحلية
- ✅ تحديثات أسهل (استبدال ملف واحد)
- ✅ نفس الوظائف والواجهة

### القيود
- ❌ يحتاج تشغيل الخادم دائماً
- ❌ حفظ الملفات يختلف عن النسخة المكتبية
- ❌ لا توجد تحديثات تلقائية
- ❌ الكاميرا تحتاج HTTPS أو localhost

## استكشاف الأخطاء وحلولها

### الخادم لا يبدأ
- تأكد من عدم استخدام المنفذ 3000 من تطبيق آخر
- جرّب تشغيل الملف كمسؤول
- تحقق من Windows Defender/مضاد الفيروسات

### المتصفح لا يفتح التطبيق
- افتح المتصفح يدوياً واذهب إلى `http://localhost:3000`
- تأكد من أن الخادم يعمل (تحقق من نافذة الأوامر)
- جرّب متصفح مختلف

### الكاميرا لا تعمل
- تحقق من أذونات الكاميرا في إعدادات المتصفح
- تأكد من عدم استخدام تطبيق آخر للكاميرا
- جرّب إعادة تحميل الصفحة

### قارئ الباركود لا يعمل
- تأكد من توصيل القارئ بشكل صحيح
- تحقق من إعدادات القارئ (قد تحتاج إلغاء البادئات/اللاحقات)
- ضع المؤشر في خانة الإدخال قبل المسح

### بطء في الأداء
- أغلق علامات تبويب أخرى في المتصفح
- تأكد من وجود ذاكرة كافية (4 جيجابايت على الأقل)
- أعد تشغيل الخادم إذا لزم الأمر

### فقدان البيانات
- تحقق من وجود ملف قاعدة البيانات في `%APPDATA%\RAFCO WMS\`
- استعد من النسخة الاحتياطية إذا كانت متوفرة
- تواصل مع فريق الدعم الفني

## الدعم الفني

### ملفات السجلات
- سجلات الخادم تظهر في نافذة الأوامر
- سجلات المتصفح: اضغط F12 > Console

### معلومات مطلوبة عند فتح بلاغ
- نوع وإصدار المتصفح
- رسالة الخطأ الكاملة
- الخطوات التي أدت للمشكلة
- لقطة شاشة إذا أمكن

### التواصل
- فريق تقنية المعلومات الداخلي
- أرفق ملفات السجلات عند الحاجة

## نصائح للاستخدام الأمثل

### الأداء
- استخدم Chrome أو Edge للأداء الأفضل
- أغلق علامات التبويب غير المستخدمة
- نظّف ذاكرة التخزين المؤقت دورياً

### الأمان
- لا تشارك رابط localhost مع أجهزة أخرى
- احتفظ بنسخ احتياطية منتظمة
- أغلق التطبيق عند عدم الاستخدام

### الإنتاجية
- استخدم اختصارات لوحة المفاتيح (Ctrl+S للحفظ، إلخ)
- تعلّم استخدام مسح الباركود لتسريع الإدخال
- استفد من التقارير الجاهزة لاتخاذ القرارات
