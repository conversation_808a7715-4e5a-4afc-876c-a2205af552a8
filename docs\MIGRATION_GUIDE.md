# RAFCO WMS Migration Guide

## Overview
This guide helps you migrate between desktop and browser versions of RAFCO WMS, or upgrade from older versions.

## Desktop to Browser Migration

### Prerequisites
- Both versions use the same SQLite database format
- Data location is identical: `%APPDATA%\RAFCO WMS\`
- No data conversion needed

### Migration Steps
1. **Stop Desktop Version**
   - Close the desktop application completely
   - Ensure no background processes are running

2. **Install Browser Version**
   - Download `RAFCO-WMS-Server.exe`
   - Place in desired location (e.g., Desktop)

3. **Start Browser Version**
   - Double-click `RAFCO-WMS-Server.exe`
   - Server will automatically detect existing database
   - <PERSON><PERSON><PERSON> opens at `http://localhost:3000`

4. **Verify Migration**
   - Check that all data appears correctly
   - Test core functionality (projects, inventory, etc.)
   - Verify file attachments are accessible

### What Transfers Automatically
- ✅ All database records (projects, inventory, users, etc.)
- ✅ File attachments and photos
- ✅ User roles and permissions
- ✅ Audit logs and history

### What Needs Reconfiguration
- ❌ Language preference (reset to English)
- ❌ Window size/position preferences
- ❌ Auto-update settings (not applicable to browser version)

## Browser to Desktop Migration

### Migration Steps
1. **Stop Browser Version**
   - Close browser tabs
   - Stop server (Ctrl+C in command window)

2. **Install Desktop Version**
   - Download `RAFCO WMS Portable.exe`
   - Place in desired location

3. **Start Desktop Version**
   - Double-click the portable executable
   - Application will detect existing database
   - All data should be immediately available

4. **Verify Migration**
   - Test all modules and functionality
   - Check file attachments
   - Verify reports and exports work

## Version Upgrade Migration

### From v0.1.x to v0.2.x
1. **Backup Current Data**
   ```
   Copy %APPDATA%\RAFCO WMS\ to backup location
   ```

2. **Install New Version**
   - Download latest version
   - Replace old executable

3. **Database Migration**
   - New version will automatically run migrations
   - Check console/logs for migration status

4. **Verify Upgrade**
   - Test new features
   - Ensure existing data is intact

### Migration Rollback
If issues occur during upgrade:
1. Stop new version
2. Restore backup folder
3. Use previous version until issues resolved

## Data Export/Import

### Full Database Export
```sql
-- Connect to SQLite database
.backup backup_file.db

-- Or copy file directly
copy "%APPDATA%\RAFCO WMS\rafco-wms.sqlite3" "backup_location\"
```

### Selective Data Export
```sql
-- Export specific tables
.mode csv
.output projects_export.csv
SELECT * FROM projects;
.output stdout
```

### Import to New Installation
1. Stop RAFCO WMS
2. Replace database file with exported version
3. Copy storage folder if needed
4. Start RAFCO WMS

## Cross-Platform Migration

### Windows to Linux (Browser Version Only)
1. **Export Data from Windows**
   - Copy database file
   - Copy storage folder
   - Note: Desktop version is Windows-only

2. **Setup Linux Server**
   ```bash
   # Install Node.js on Linux
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   
   # Copy and run RAFCO WMS
   npm install
   npm run build:server
   npm run start:server
   ```

3. **Import Data**
   - Place database file in appropriate location
   - Copy storage folder
   - Adjust file paths in configuration

### macOS Support (Browser Version)
Similar to Linux migration, browser version can run on macOS with Node.js.

## Network Deployment Migration

### Single User to Multi-User
1. **Setup Central Server**
   - Choose dedicated machine for server
   - Install browser version
   - Configure network access

2. **Migrate Data**
   - Copy database from user machine to server
   - Copy all storage files
   - Test server accessibility

3. **Configure Client Access**
   - Provide server IP address to users
   - Update bookmarks to `http://[SERVER_IP]:3000`
   - Train users on browser-based interface

### Multi-User to Single User
1. **Export Central Data**
   - Backup server database and files
   - Stop central server

2. **Distribute to Users**
   - Copy database to each user's machine
   - Install desktop or browser version locally
   - Users work independently

## Troubleshooting Migration Issues

### Database Corruption
```sql
-- Check database integrity
PRAGMA integrity_check;

-- Repair if needed
PRAGMA quick_check;
```

### Missing Files
- Check storage folder permissions
- Verify file paths in database
- Restore from backup if necessary

### Performance Issues After Migration
- Run database optimization:
  ```sql
  VACUUM;
  ANALYZE;
  ```
- Clear browser cache (browser version)
- Restart application

### Version Compatibility
- Check migration logs for errors
- Verify all required migrations ran
- Contact support if persistent issues

## Best Practices

### Before Migration
1. **Create Full Backup**
   - Database file
   - Storage folder
   - Configuration files

2. **Test Migration**
   - Use copy of data for testing
   - Verify all functionality works
   - Document any issues

3. **Plan Downtime**
   - Schedule migration during low usage
   - Notify users of temporary unavailability
   - Prepare rollback plan

### During Migration
1. **Monitor Progress**
   - Watch for error messages
   - Check migration logs
   - Verify data integrity

2. **Document Issues**
   - Record any problems encountered
   - Note solutions applied
   - Update procedures for future migrations

### After Migration
1. **Verify Functionality**
   - Test all core features
   - Check reports and exports
   - Validate user access

2. **User Training**
   - Brief users on any interface changes
   - Provide updated documentation
   - Offer support during transition

3. **Monitor Performance**
   - Watch for performance issues
   - Optimize if necessary
   - Gather user feedback

## Support

### Migration Assistance
- Contact IT support team for complex migrations
- Provide detailed error logs and descriptions
- Include system specifications and version information

### Emergency Recovery
- Keep backup readily available
- Document rollback procedures
- Test recovery process periodically

### Documentation Updates
- Update user manuals after migration
- Revise training materials
- Maintain migration history log
