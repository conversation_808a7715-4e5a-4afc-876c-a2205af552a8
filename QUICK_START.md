# RAFCO WMS - Quick Start Guide

## 🚀 For End Users (No Technical Knowledge Required)

### Desktop Version
1. **Download:** Get `RAFCO WMS Portable.exe` from IT
2. **Run:** Double-click the file
3. **Wait:** Application opens in 3-5 seconds
4. **Use:** Start managing your warehouse immediately!

### Browser Version  
1. **Download:** Get `RAFCO-WMS-Server.exe` from IT
2. **Run:** Double-click the file
3. **Wait:** Console window appears, browser opens automatically
4. **Access:** Use the application at `http://localhost:3000`

**No installation, no admin rights, no setup required!**

---

## 🛠️ For Developers

### Development Setup
```bash
# Clone and install
git clone [repository]
cd rafco-wms
npm install

# Desktop development
npm run dev

# Browser development  
npm run dev:server
```

### Build Portable Executables
```bash
# Quick build (both versions)
scripts\build-portable.bat

# Manual builds
npm run portable          # Desktop version
npm run portable:server   # Browser version
```

### Output Files
- `release\RAFCO WMS Portable.exe` - Desktop version (~200MB)
- `release\RAFCO-WMS-Server.exe` - Browser version (~120MB)

---

## 📋 System Requirements

### Minimum Requirements
- **OS:** Windows 10/11 x64
- **RAM:** 4GB (8GB recommended)
- **Disk:** 500MB free space
- **Browser:** Chrome 90+, Firefox 88+, Edge 90+ (browser version only)

### Optional Hardware
- **Camera:** For barcode/QR scanning
- **USB Scanner:** Fully supported (keyboard wedge mode)

---

## 🌍 Language Support

### Arabic (العربية)
- Complete RTL interface
- Arabic text input and display
- Arabic number formatting
- Hijri calendar support
- Right-to-left navigation

### English
- Full LTR interface
- Western number formatting
- Gregorian calendar
- Left-to-right navigation

**Switch languages instantly with the العربية/English button!**

---

## 📊 Core Features

### ✅ Project Management
- Track projects from start to finish
- Budget monitoring and cost control
- Link to purchase orders and shipments

### ✅ Inventory Management
- Multi-warehouse support (Main, Dammam, Khodaria, Jubail)
- Barcode/QR scanning
- Real-time stock tracking
- Bin location management

### ✅ Procurement
- Electronic purchase orders
- Goods receipt notes (GRN)
- Quality control workflows
- Photo and document uploads

### ✅ Manufacturing & Shipping
- Material consumption tracking
- Waste analysis by factory
- Bills of lading with photos
- Delivery confirmations

### ✅ Reporting & Analytics
- Inventory status reports
- Project expense analysis
- Waste analysis reports
- Demand forecasting

---

## 🔧 Troubleshooting

### Desktop Version Issues
**App won't start:**
- Check Windows SmartScreen (click "More info" > "Run anyway")
- Ensure Windows 10/11 x64
- Run from writable location (Desktop)

**Slow performance:**
- Close other applications
- Ensure 4GB+ RAM available
- Check antivirus isn't scanning

### Browser Version Issues
**Server won't start:**
- Check if port 3000 is available
- Try running as administrator
- Check Windows Firewall settings

**Browser won't open:**
- Manually go to `http://localhost:3000`
- Try different browser (Chrome recommended)
- Check if server is running (console window)

**Camera not working:**
- Allow camera permissions in browser
- Ensure no other app is using camera
- Try refreshing the page

---

## 📞 Support

### Self-Help Resources
- **English Documentation:** `docs/BROWSER_VERSION_GUIDE.md`
- **Arabic Documentation:** `docs/arabic/README_AR.md`
- **Deployment Guide:** `docs/PORTABLE_DEPLOYMENT.md`

### Getting Help
1. Check the troubleshooting section above
2. Review the appropriate documentation
3. Contact your IT support team
4. Include error messages and steps to reproduce

### Log Files
**Desktop Version:**
```
%APPDATA%\RAFCO WMS\logs\
```

**Browser Version:**
- Console output (visible in command window)
- Browser developer tools (F12 > Console)

---

## 🎯 Quick Actions

### First Time Setup
1. **Language:** Click العربية/English to switch
2. **Camera:** Allow permissions when prompted
3. **Test:** Try scanning a barcode or adding a material

### Daily Operations
- **Add Material:** Use quick action button or scan barcode
- **Issue to Project:** Select project and scan items
- **Issue Consumables:** Track operational costs
- **Generate Reports:** Export to Excel/CSV

### Data Management
- **Backup:** Copy `%APPDATA%\RAFCO WMS\` folder
- **Restore:** Replace folder contents
- **Export:** Use built-in export functions

---

## 🔄 Updates

### Desktop Version
1. Download new `RAFCO WMS Portable.exe`
2. Close current application
3. Replace old file with new one
4. Restart application

### Browser Version
1. Download new `RAFCO-WMS-Server.exe`
2. Stop server (Ctrl+C in console)
3. Replace old file with new one
4. Restart server

**Your data is preserved during updates!**

---

## 💡 Tips for Best Experience

### Performance
- Use Chrome or Edge for browser version
- Close unused browser tabs
- Keep adequate free disk space
- Regular data backups

### Productivity
- Learn keyboard shortcuts
- Use barcode scanning for speed
- Set up regular report exports
- Train team on core workflows

### Security
- Keep application files in secure location
- Regular backups to secure storage
- Don't share localhost URLs externally
- Report security concerns to IT

---

**Ready to start? Just double-click your executable and begin managing your warehouse efficiently!**
