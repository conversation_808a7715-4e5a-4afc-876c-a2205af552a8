import express from 'express';
import cors from 'cors';
import path from 'path';
import { fileURLToPath } from 'url';
import multer from 'multer';
import fs from 'fs';
import open from 'open';
import { ensureDb, runMigrations, getDb } from './services/db';
import { setupApiRoutes } from './routes/api';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Static files (React build)
const staticPath = path.join(__dirname, '../renderer');
app.use(express.static(staticPath));

// API routes
setupApiRoutes(app);

// Serve React app for all other routes
app.get('*', (req, res) => {
  res.sendFile(path.join(staticPath, 'index.html'));
});

async function startServer() {
  try {
    console.log('🔧 Initializing RAFCO WMS Server...');
    
    // Initialize database
    await ensureDb();
    await runMigrations();
    console.log('✅ Database initialized');

    // Start server
    app.listen(PORT, () => {
      console.log(`🚀 RAFCO WMS Server running on http://localhost:${PORT}`);
      console.log('📱 Opening browser...');
      
      // Auto-open browser
      setTimeout(() => {
        open(`http://localhost:${PORT}`).catch(() => {
          console.log('⚠️  Could not auto-open browser. Please manually open: http://localhost:' + PORT);
        });
      }, 1000);
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

startServer();
