import path from 'path';
import { app } from 'electron';
import Database from 'better-sqlite3';
import fs from 'fs';
import { spawn } from 'child_process';
import knexFactory from 'knex';

let db: Database.Database | null = null;

export function getDbPath() {
  const userData = app.getPath('userData');
  return path.join(userData, 'rafco-wms.sqlite3');
}

export async function ensureDb() {
  const p = getDbPath();
  const dir = path.dirname(p);
  if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
  if (!fs.existsSync(p)) {
    fs.writeFileSync(p, '');
  }
  db = new Database(p);
}

export async function runMigrations() {
  const knex = knexFactory({
    client: 'better-sqlite3',
    connection: { filename: getDbPath() },
    useNullAsDefault: true,
    migrations: { directory: path.join(process.cwd(), 'src/main/db/migrations') }
  } as any);
  await knex.migrate.latest();
  await knex.destroy();
}

export function getDb() {
  if (!db) throw new Error('DB not initialized');
  return db;
}

