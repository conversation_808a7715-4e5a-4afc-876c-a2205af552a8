import { app, BrowserWindow, ipcMain, dialog, shell } from 'electron';
import path from 'path';
import { fileURLToPath } from 'url';
import log from 'electron-log/main';
import { autoUpdater } from 'electron-updater';
import { ensureDb, runMigrations } from './services/db';
import { setupIpcHandlers } from './services/ipc';

log.initialize();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const isDev = process.env.VITE_DEV_SERVER_URL != null;

let mainWindow: BrowserWindow | null = null;

async function createWindow() {
  const preload = path.join(__dirname, 'preload.js');

  mainWindow = new BrowserWindow({
    width: 1280,
    height: 800,
    show: false,
    webPreferences: {
      preload,
      contextIsolation: true,
      nodeIntegration: false,
      sandbox: false,
    },
  });

  mainWindow.on('ready-to-show', () => mainWindow?.show());

  if (isDev && process.env.VITE_DEV_SERVER_URL) {
    await mainWindow.loadURL(process.env.VITE_DEV_SERVER_URL);
    mainWindow.webContents.openDevTools();
  } else {
    const indexHtml = path.join(__dirname, '../renderer/index.html');
    await mainWindow.loadFile(indexHtml);
  }

  setupIpcHandlers(ipcMain, mainWindow);
}

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.whenReady().then(async () => {
  try {
    await ensureDb();
    await runMigrations();
  } catch (e:any) {
    log.error('DB init failed', e);
    dialog.showErrorBox('Database Error', String(e?.message ?? e));
  }
  await createWindow();
  if (!isDev) {
    autoUpdater.checkForUpdatesAndNotify();
  }
});

