# RAFCO WMS Browser Version - Complete Setup and Deployment Guide

## Overview
This guide covers the browser-based version of RAFCO WMS, which runs as a local Node.js server and can be accessed through any modern web browser. This version maintains all functionality of the desktop version while offering easier deployment and cross-platform compatibility.

## System Requirements

### Server Requirements
- Windows 10/11 x64 (Linux/macOS support available)
- 4GB RAM minimum (8GB recommended)
- 500MB disk space for application bundle
- Additional space for database and uploaded files
- No admin privileges required for portable version

### Browser Requirements
- Chrome 90+ (recommended)
- Firefox 88+
- Edge 90+
- Safari 14+ (macOS)

### Hardware Requirements
- Camera (optional) for barcode/QR scanning
- USB barcode scanners (fully supported)
- Network connectivity for multi-user access (optional)

## Installation and Setup

### Method 1: Portable Executable (Recommended)
1. Download `RAFCO-WMS-Server.exe` from IT department
2. Place the file in a suitable directory (e.g., Desktop or `C:\RAFCO WMS\`)
3. Double-click the executable to start the server
4. Wait for the message "Server running on http://localhost:3000"
5. <PERSON><PERSON><PERSON> will open automatically, or manually navigate to: `http://localhost:3000`

### Method 2: From Source Code (Developers)
```bash
# Install dependencies
npm install

# Build the server version
npm run build:server

# Start the server
npm run start:server

# Or run in development mode
npm run dev:server
```

### Method 3: Building Portable Executable
```bash
# Build and package as portable executable
npm run portable:server

# Output will be in release/RAFCO-WMS-Server.exe
```

## First-Time Setup

### Language Configuration
- Default language is English on first launch
- Click "العربية" button in the header to switch to Arabic
- Language switching is instant and doesn't require page reload
- Preference is saved locally in browser storage

### Server Connection Test
- Verify "Connected" status appears in the server status indicator
- Click "Test Database" button to verify database connectivity
- Green checkmarks should appear for all browser compatibility checks

### Browser Permissions
- Camera access will be requested when first using barcode scanning
- Click "Allow" to enable barcode/QR code scanning functionality
- File upload permissions are handled automatically

## Core Functionality

### Project Management
- Create and track projects from initiation to completion
- Link projects to purchase orders, issues, and shipments
- Monitor budgets and costs in real-time
- Generate project-specific reports

### Inventory Management
- Register raw materials and consumables
- Track inventory across main warehouse and branches (Dammam, Khodaria, Jubail)
- Barcode/QR scanning for materials
- Precise bin location tracking
- Real-time stock level monitoring

### Procurement Module
- Electronic purchase order creation linked to projects
- Digital Goods Receipt Notes (GRN) with scanning capability
- Quality control with accept/reject status and quarantine locations
- Photo and document uploads (invoices, quality certificates)
- Supplier management and tracking

### Manufacturing & Shipping
- Material consumption tracking by factory
- Waste logging by type and factory location
- Dedicated tracking for screws (type/weight) and paints (color/type)
- Digital bills of lading with product descriptions, quantities, weights
- Delivery status tracking with photo uploads

### Reporting & Analytics
- Interactive dashboards with real-time data
- Inventory status reports with reorder alerts
- Project expense reports (materials + consumables breakdown)
- Waste analysis reports by factory and type
- Demand forecasting based on historical data

## Barcode/QR Code Scanning

### Using Camera
1. Click the scan icon in any input field
2. Allow browser camera access when prompted
3. Point camera at barcode/QR code
4. Scanning happens automatically upon recognition
5. Result is populated in the input field

### Using USB Scanner
1. Ensure barcode scanner is connected to computer
2. Click in the desired input field
3. Scan the barcode - text appears automatically
4. Scanner acts as keyboard input (keyboard wedge mode)

### Troubleshooting Scanning
- **Camera not working**: Check browser permissions, ensure no other app is using camera
- **USB scanner not working**: Verify connection, check scanner settings for prefixes/suffixes
- **Poor scan quality**: Ensure good lighting, clean barcode surface, steady hand

## File Management

### Photo Uploads
1. Click "Select Files" or "Upload Photos" button
2. Choose images from your device (JPG, PNG supported)
3. Click "Upload Files" to save them to the system
4. Files are stored server-side and linked to relevant records

### Direct Camera Capture
1. Click "Start Camera" button
2. Point camera at subject
3. Click "Capture" to take and save photo
4. Photo is automatically uploaded and linked

### Document Management
- Support for various file types (PDF, DOC, images)
- Organized storage by module and entity
- Automatic file naming and organization
- Secure server-side storage

## Data Export and Printing

### CSV Export
- Click "Export CSV" button in any report
- File downloads automatically to browser's download folder
- Supports Arabic text and proper encoding

### Excel Export
- Click "Export Excel" for .xlsx format
- Maintains Arabic text formatting and RTL layout
- Includes charts and formatting where applicable

### Printing
- Use browser's print function (Ctrl+P)
- Print layouts support Arabic text and RTL direction
- Optimized for A4 paper size
- Print preview available

## Data Storage and Backup

### Database Location
```
%APPDATA%\RAFCO WMS\rafco-wms.sqlite3
```

### Uploaded Files Location
```
%APPDATA%\RAFCO WMS\storage\
```

### Backup Procedure
1. Stop the server (close browser and press Ctrl+C in server window)
2. Copy entire `%APPDATA%\RAFCO WMS\` folder
3. Store backup in secure location
4. Optionally compress folder for archival

### Restore Procedure
1. Stop the server
2. Replace `%APPDATA%\RAFCO WMS\` folder with backup copy
3. Restart the server
4. Verify data integrity

## Desktop vs Browser Version Comparison

### Advantages of Browser Version
- ✅ Smaller file size (no Electron overhead)
- ✅ Multi-device access within local network
- ✅ Easier updates (single file replacement)
- ✅ Cross-platform compatibility
- ✅ Familiar browser interface
- ✅ Better memory management

### Limitations of Browser Version
- ❌ Requires server to be running continuously
- ❌ File saving behavior differs from desktop
- ❌ No automatic updates
- ❌ Camera requires HTTPS or localhost
- ❌ Some browser-specific limitations

### Feature Parity
Both versions include:
- Complete Arabic/English bilingual support
- All core modules and workflows
- Barcode/QR scanning capabilities
- File upload and export functionality
- Real-time data synchronization
- Role-based access control

## Network Access and Multi-User Setup

### Local Network Access
To allow access from other devices on the network:

1. Find your computer's IP address:
   ```cmd
   ipconfig
   ```

2. Start server with network binding:
   ```bash
   set PORT=3000
   set HOST=0.0.0.0
   npm run start:server
   ```

3. Access from other devices using:
   ```
   http://[YOUR_IP_ADDRESS]:3000
   ```

### Security Considerations
- Server runs on local network only by default
- No external internet access required
- Data remains on local machine
- Consider firewall settings for network access

## Troubleshooting

### Server Won't Start
- **Port already in use**: Another application is using port 3000
  - Solution: Close other applications or change port
- **Permission denied**: Insufficient file system permissions
  - Solution: Run from writable directory or as administrator
- **Antivirus blocking**: Security software preventing execution
  - Solution: Add exception for RAFCO WMS executable

### Browser Connection Issues
- **Page won't load**: Server not running or wrong URL
  - Solution: Verify server is running, check URL spelling
- **Slow loading**: Network or performance issues
  - Solution: Close other browser tabs, restart server
- **Features not working**: Browser compatibility issues
  - Solution: Use Chrome/Edge, enable JavaScript

### Camera/Scanner Problems
- **Camera permission denied**: Browser blocking camera access
  - Solution: Check browser settings, allow camera access
- **Scanner not responding**: USB connection or configuration issue
  - Solution: Check USB connection, verify scanner settings
- **Poor scan quality**: Environmental or hardware factors
  - Solution: Improve lighting, clean scanner lens

### Performance Issues
- **Slow response**: Server overloaded or insufficient resources
  - Solution: Close other applications, restart server
- **Memory usage high**: Large dataset or memory leak
  - Solution: Restart browser and server, optimize queries
- **Database locked**: Concurrent access issues
  - Solution: Ensure only one server instance running

## Migration from Desktop Version

### Database Migration
1. Stop both desktop and server versions
2. Copy database file from desktop version location to server location:
   ```
   From: %APPDATA%\RAFCO WMS\rafco-wms.sqlite3
   To: Same location (compatible)
   ```
3. Copy storage folder if it exists
4. Start server version

### Settings Migration
- Language preferences: Will need to be reset in browser
- User preferences: Stored in browser local storage
- Custom configurations: May need manual reconfiguration

## Performance Optimization

### Server Performance
- Allocate sufficient RAM (8GB recommended)
- Use SSD storage for better database performance
- Close unnecessary applications
- Regular database maintenance

### Browser Performance
- Use Chrome or Edge for best performance
- Close unused browser tabs
- Clear browser cache periodically
- Disable unnecessary browser extensions

### Network Performance
- Use wired connection when possible
- Ensure stable network connectivity
- Consider local network bandwidth for multi-user scenarios

## Security Best Practices

### Access Control
- Server runs on localhost by default (secure)
- Enable network access only when necessary
- Use strong passwords for user accounts
- Regular backup of sensitive data

### Data Protection
- Database stored locally (not in cloud)
- Regular automated backups recommended
- Secure physical access to server machine
- Consider encryption for sensitive installations

## Support and Maintenance

### Log Files
- Server logs appear in command window
- Browser logs: Press F12 > Console tab
- Save logs when reporting issues

### Regular Maintenance
- Weekly database backups
- Monthly performance review
- Quarterly security updates
- Annual system review

### Getting Help
When reporting issues, include:
- Browser type and version
- Complete error message
- Steps to reproduce the problem
- Screenshot if applicable
- Server log output

### Contact Information
- Internal IT support team
- Include log files when submitting tickets
- Specify "Browser Version" in support requests
