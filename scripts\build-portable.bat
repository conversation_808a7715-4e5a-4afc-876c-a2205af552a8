@echo off
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                RAFCO WMS Portable Builder                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo [1/6] Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo [2/6] Building desktop version...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ Failed to build desktop version
    pause
    exit /b 1
)

echo.
echo [3/6] Creating desktop portable executable...
call npm run portable
if %errorlevel% neq 0 (
    echo ❌ Failed to create desktop portable
    pause
    exit /b 1
)

echo.
echo [4/6] Building server version...
call npm run build:server
if %errorlevel% neq 0 (
    echo ❌ Failed to build server version
    pause
    exit /b 1
)

echo.
echo [5/6] Creating server portable executable...
call npm run portable:server
if %errorlevel% neq 0 (
    echo ❌ Failed to create server portable
    pause
    exit /b 1
)

echo.
echo [6/6] Build complete!
echo.
echo ✅ Desktop version: release\RAFCO WMS Portable.exe
echo ✅ Server version:  release\RAFCO-WMS-Server.exe
echo.
echo Both executables are ready for distribution.
echo No installation required - just double-click to run!
echo.
pause
