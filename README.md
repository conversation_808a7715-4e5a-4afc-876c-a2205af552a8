# RAFCO WMS Desktop (Electron + React + SQLite)

This repository contains a standalone Windows desktop application for RAFCO Warehouse Management. No admin rights or installation required (portable build).

## Quick Start (Dev)

1. Install Node.js 18+ and npm
2. npm install
3. npm run dev

## Build Portable EXE

- npm run portable
- Output in `release/` as a single portable .exe

## Data & Storage

- SQLite DB path: `%APPDATA%/RAFCO WMS/rafco-wms.sqlite3`
- Attachments: `%APPDATA%/RAFCO WMS/storage/...`

## Features (Scaffolded)

- Electron main + React renderer with MUI
- Arabic/English i18n with RTL
- SQLite with Knex migrations
- IPC bridges for DB/files/i18n
- Packaging and auto-update config (generic provider)

Detailed modules, workflows, reports, and i18n content will be implemented in subsequent commits.

