# RAFCO WMS - Dual Platform (Desktop + Browser)

This repository contains both desktop and browser-based versions of the RAFCO Warehouse Management System. Choose the deployment method that best fits your needs.

## 🖥️ Desktop Version (Electron + React + SQLite)
Standalone Windows desktop application with no installation required.

### Quick Start (Desktop)
```bash
npm install
npm run dev          # Development mode
npm run portable     # Build portable .exe
```

## 🌐 Browser Version (Node.js Server + React + SQLite)
Local web server accessible through any modern browser.

### Quick Start (Browser)
```bash
npm install
npm run dev:server   # Development mode
npm run portable:server  # Build portable server .exe
```

Access at: `http://localhost:3000`

## 📋 System Requirements

### Desktop Version
- Windows 10/11 x64
- 4GB RAM minimum
- 300MB disk space
- No admin rights needed

### Browser Version
- Windows 10/11 x64 (server)
- Modern browser (Chrome 90+, Firefox 88+, Edge 90+)
- 4GB RAM minimum
- 500MB disk space
- Camera support for barcode scanning

## 🗂️ Data & Storage (Both Versions)
- SQLite DB: `%APPDATA%/RAFCO WMS/rafco-wms.sqlite3`
- Attachments: `%APPDATA%/RAFCO WMS/storage/...`
- Compatible database between versions

## ✨ Features (Both Versions)
- **Bilingual Support**: Arabic/English with RTL layouts
- **Core Modules**: Project Management, Inventory, Procurement, Manufacturing, Shipping, Reporting
- **Barcode/QR Scanning**: Camera + USB scanner support
- **File Management**: Photo uploads, document attachments
- **Export Capabilities**: CSV, Excel, PDF with Arabic support
- **Real-time Sync**: Multi-warehouse inventory tracking
- **Role-based Access**: User permissions and audit trails

## 📚 Documentation
- [Desktop Setup Guide](docs/DESKTOP_SETUP.md)
- [Browser Version Guide](docs/BROWSER_VERSION_GUIDE.md)
- [Arabic Documentation](docs/arabic/README_AR.md)
- [Migration Guide](docs/MIGRATION_GUIDE.md)

## 🚀 Quick Deployment

### For End Users (Desktop)
1. Download `RAFCO WMS Portable.exe`
2. Double-click to run
3. No installation needed

### For End Users (Browser)
1. Download `RAFCO-WMS-Server.exe`
2. Double-click to start server
3. Browser opens automatically at `localhost:3000`

## 🔧 Development Scripts

### Desktop Development
```bash
npm run dev          # Electron + React dev servers
npm run build        # Build production assets
npm run portable     # Create portable desktop .exe
```

### Browser Development
```bash
npm run dev:server   # Node.js server + React dev servers
npm run build:server # Build server production assets
npm run portable:server  # Create portable server .exe
```

### Database Management
```bash
npm run migrate      # Run database migrations
```

## 🌍 Internationalization
- Runtime language switching (Arabic ↔ English)
- RTL/LTR layout support
- Arabic number formatting and calendar support
- Bidirectional text handling

## 🔒 Security & Compliance
- Local data storage (no cloud dependencies)
- Role-based access control
- Comprehensive audit logging
- Windows Defender compatible

## 📊 Performance
- Cold start: <5 seconds
- Supports 4GB+ RAM systems
- Optimized for business hardware
- Efficient SQLite operations

Choose your preferred deployment method and refer to the appropriate documentation for detailed setup instructions.

