import type { <PERSON>p<PERSON><PERSON><PERSON>, <PERSON><PERSON>erWindow } from 'electron';
import { dialog } from 'electron';
import { getDb } from './db';
import fs from 'fs';
import path from 'path';

export function setupIpcHandlers(ipcMain: IpcMain, mainWindow: BrowserWindow | null) {
  ipcMain.handle('ping', async () => 'pong');

  ipcMain.handle('db:query', async (_e, { sql, params }) => {
    const db = getDb();
    const stmt = db.prepare(sql);
    return stmt.all(params ?? []);
  });
  ipcMain.handle('db:run', async (_e, { sql, params }) => {
    const db = getDb();
    const stmt = db.prepare(sql);
    const info = stmt.run(params ?? []);
    return info;
  });

  ipcMain.handle('files:choosePhotos', async () => {
    const res = await dialog.showOpenDialog({ properties: ['openFile', 'multiSelections'], filters: [{ name: 'Images', extensions: ['jpg','jpeg','png'] }] });
    if (res.canceled) return [];
    return res.filePaths;
  });

  ipcMain.handle('files:exportCSV', async (_e, { name, csv }) => {
    const res = await dialog.showSaveDialog({ defaultPath: `${name}.csv`, filters: [{ name: 'CSV', extensions: ['csv'] }] });
    if (res.canceled || !res.filePath) return false;
    fs.writeFileSync(res.filePath, csv, 'utf8');
    return true;
  });

  let currentLocale = 'en';
  ipcMain.handle('i18n:getLocale', async () => currentLocale);
  ipcMain.handle('i18n:setLocale', async (_e, { locale }) => { currentLocale = locale; return true; });
}

