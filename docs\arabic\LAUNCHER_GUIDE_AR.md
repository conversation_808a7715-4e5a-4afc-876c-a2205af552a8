# دليل مشغل RAFCO WMS

## نظرة عامة
يوفر مشغل RAFCO WMS واجهة موحدة للوصول إلى نظام إدارة المستودعات، سواء كان الإصدار المكتبي أو إصدار المتصفح.

## الميزات الرئيسية

### 🌐 الكشف التلقائي للتطبيق
- يفحص تلقائياً إذا كان خادم RAFCO WMS يعمل
- يجرب منافذ متعددة (3000-3005) للعثور على الخادم
- يعرض حالة الاتصال بوضوح

### 🔄 التحديث التلقائي
- يفحص حالة الخادم كل 30 ثانية
- يحدث الحالة تلقائياً عند تشغيل/إيقاف الخادم
- زر تحديث يدوي متاح

### 🌍 الدعم ثنائي اللغة
- تبديل فوري بين العربية والإنجليزية
- حفظ تفضيل اللغة في المتصفح
- دعم كامل لتخطيط RTL للعربية

### 🎨 واجهة احترافية
- تصميم حديث مع تدرجات لونية
- رموز وأيقونات واضحة
- رسائل حالة ملونة (تحميل، نجاح، خطأ)
- تأثيرات بصرية سلسة

## كيفية الاستخدام

### 1. فتح المشغل
- افتح ملف `launcher.html` في أي متصفح حديث
- أو ضعه في مجلد الخادم واذهب إلى `http://localhost:3000/launcher.html`

### 2. فحص الحالة
المشغل سيفحص تلقائياً:
- هل خادم RAFCO WMS يعمل؟
- على أي منفذ يعمل الخادم؟
- هل التطبيق جاهز للاستخدام؟

### 3. تشغيل التطبيق
- إذا كان الخادم يعمل: انقر "تشغيل التطبيق"
- إذا لم يكن يعمل: شغّل `RAFCO-WMS-Server.exe` أولاً

## رسائل الحالة

### ✅ التطبيق جاهز
```
التطبيق جاهز للاستخدام!
[تشغيل التطبيق] [تحديث]
```

### ⏳ فحص الحالة
```
فحص حالة التطبيق...
[دوران مؤشر التحميل]
```

### ❌ الخادم غير يعمل
```
خادم التطبيق غير يعمل
انقر نقراً مزدوجاً على RAFCO-WMS-Server.exe لبدء الخادم، ثم حدث هذه الصفحة.
[تحديث]
```

## تبديل اللغة

### من الإنجليزية للعربية
1. انقر زر "العربية" في أعلى اليمين
2. الواجهة تتحول فوراً للعربية مع تخطيط RTL
3. جميع النصوص والرسائل تظهر بالعربية

### من العربية للإنجليزية
1. انقر زر "English" في أعلى اليسار
2. الواجهة تتحول فوراً للإنجليزية مع تخطيط LTR
3. جميع النصوص والرسائل تظهر بالإنجليزية

## استكشاف الأخطاء

### المشغل لا يفتح
- تأكد من أن المتصفح يدعم JavaScript
- جرب متصفح مختلف (Chrome، Firefox، Edge)
- تحقق من وجود ملف `launcher.html`

### لا يجد الخادم
- تأكد من تشغيل `RAFCO-WMS-Server.exe`
- انتظر حتى تظهر رسالة "Server running" في وحدة التحكم
- جرب الوصول المباشر: `http://localhost:3000`

### الخادم يعمل لكن المشغل لا يكتشفه
- تحقق من إعدادات جدار الحماية
- تأكد من أن المنفذ 3000 غير محجوب
- جرب إعادة تشغيل المتصفح

### تبديل اللغة لا يعمل
- تأكد من تمكين JavaScript في المتصفح
- امسح ذاكرة التخزين المؤقت للمتصفح
- أعد تحميل الصفحة

## التخصيص

### تغيير الألوان
يمكن تعديل ألوان المشغل في قسم CSS:

```css
/* التدرج الرئيسي */
background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

/* لون الشعار */
background: linear-gradient(135deg, #667eea, #764ba2);

/* ألوان الحالة */
.status.success { background: #e8f5e8; color: #2e7d32; }
.status.error { background: #ffebee; color: #c62828; }
```

### إضافة شعار الشركة
استبدل رمز الصندوق (📦) بشعار RAFCO:

```html
<div class="logo">
    <img src="assets/rafco-logo.png" alt="RAFCO" style="width: 60px; height: 60px;">
</div>
```

### تعديل النصوص
غيّر النصوص في كائن `translations`:

```javascript
const translations = {
    ar: {
        title: 'نظام مستودعات RAFCO المحدث',
        subtitle: 'نظام إدارة المستودعات المتطور',
        // ... باقي النصوص
    }
};
```

## الأمان

### الوصول المحلي فقط
- المشغل يتصل بـ localhost فقط
- لا يرسل بيانات لخوادم خارجية
- جميع العمليات محلية وآمنة

### حفظ التفضيلات
- تفضيل اللغة يحفظ في localStorage
- لا تحفظ معلومات حساسة
- يمكن مسح البيانات من إعدادات المتصفح

## التكامل مع التطبيق

### استخدام كصفحة رئيسية
ضع `launcher.html` في مجلد الخادم واجعله الصفحة الافتراضية:

```javascript
// في server.js
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'launcher.html'));
});
```

### تضمين في التطبيق
يمكن تضمين المشغل كجزء من واجهة التطبيق:

```html
<iframe src="launcher.html" width="100%" height="600px"></iframe>
```

## نصائح للاستخدام الأمثل

### للمستخدمين النهائيين
1. احفظ `launcher.html` في المفضلة
2. استخدمه كنقطة دخول موحدة للنظام
3. تأكد من تشغيل الخادم قبل استخدام المشغل

### لفريق IT
1. ضع المشغل في مجلد مشترك للوصول السهل
2. خصص الألوان والشعارات حسب هوية الشركة
3. أضف تعليمات إضافية حسب بيئة العمل

### للمطورين
1. استخدم المشغل لاختبار اتصال الخادم
2. أضف نقاط فحص إضافية حسب الحاجة
3. راقب أداء الفحص التلقائي

## الدعم الفني

### ملفات السجلات
- سجلات المتصفح: F12 > Console
- أخطاء JavaScript تظهر في وحدة التحكم
- فحص شبكة الاتصال: F12 > Network

### الإبلاغ عن المشاكل
عند الإبلاغ عن مشكلة، أرفق:
1. نوع وإصدار المتصفح
2. رسائل الخطأ من وحدة التحكم
3. حالة خادم RAFCO WMS
4. خطوات إعادة الإنتاج

المشغل يوفر طريقة سهلة ومهنية للوصول إلى نظام RAFCO WMS مع دعم كامل للغة العربية وتجربة مستخدم محسنة.
