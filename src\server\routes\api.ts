import { Express, Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import os from 'os';
import { getDb } from '../services/db';
import ExcelJS from 'exceljs';

const upload = multer({ 
  dest: path.join(os.homedir(), 'AppData', 'Roaming', 'RAFCO WMS', 'temp'),
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB limit
});

let currentLocale = 'en';

export function setupApiRoutes(app: Express) {
  // Health check
  app.get('/api/ping', (req, res) => {
    res.json({ message: 'pong', timestamp: new Date().toISOString() });
  });

  // Database operations
  app.post('/api/db/query', async (req: Request, res: Response) => {
    try {
      const { sql, params } = req.body;
      const db = getDb();
      const stmt = db.prepare(sql);
      const result = stmt.all(params ?? []);
      res.json({ success: true, data: result });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  });

  app.post('/api/db/run', async (req: Request, res: Response) => {
    try {
      const { sql, params } = req.body;
      const db = getDb();
      const stmt = db.prepare(sql);
      const info = stmt.run(params ?? []);
      res.json({ success: true, data: info });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  });

  // File upload (replaces choosePhotos)
  app.post('/api/files/upload', upload.array('photos', 10), (req: Request, res: Response) => {
    try {
      const files = req.files as Express.Multer.File[];
      const filePaths = files.map(file => file.path);
      res.json({ success: true, data: filePaths });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  });

  // Export CSV
  app.post('/api/files/export/csv', (req: Request, res: Response) => {
    try {
      const { name, csv } = req.body;
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${name}.csv"`);
      res.send(csv);
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  });

  // Export Excel
  app.post('/api/files/export/excel', async (req: Request, res: Response) => {
    try {
      const { name, data, headers } = req.body;
      
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Data');
      
      // Add headers
      if (headers) {
        worksheet.addRow(headers);
      }
      
      // Add data
      data.forEach((row: any[]) => {
        worksheet.addRow(row);
      });
      
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${name}.xlsx"`);
      
      await workbook.xlsx.write(res);
      res.end();
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  });

  // Locale management
  app.get('/api/settings/locale', (req, res) => {
    res.json({ success: true, data: currentLocale });
  });

  app.post('/api/settings/locale', (req: Request, res: Response) => {
    try {
      const { locale } = req.body;
      currentLocale = locale;
      res.json({ success: true, data: currentLocale });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  });
}
