import React, { useRef } from 'react';
import { Button, Box, Typography, List, ListItem, ListItemText, IconButton } from '@mui/material';
import { CloudUpload, Delete, PhotoCamera } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { api } from '../services/api';

interface FileUploadProps {
  onFilesUploaded: (filePaths: string[]) => void;
  accept?: string;
  multiple?: boolean;
  maxFiles?: number;
}

export const FileUpload: React.FC<FileUploadProps> = ({ 
  onFilesUploaded, 
  accept = 'image/*',
  multiple = true,
  maxFiles = 10 
}) => {
  const { t } = useTranslation();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFiles, setSelectedFiles] = React.useState<File[]>([]);
  const [uploading, setUploading] = React.useState(false);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const limitedFiles = files.slice(0, maxFiles);
    setSelectedFiles(limitedFiles);
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) return;

    try {
      setUploading(true);
      const fileList = new DataTransfer();
      selectedFiles.forEach(file => fileList.items.add(file));
      
      const filePaths = await api.files.uploadPhotos(fileList.files);
      onFilesUploaded(filePaths);
      setSelectedFiles([]);
      
      // Reset input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Upload failed:', error);
      alert(t('upload.error', 'Upload failed: ') + error);
    } finally {
      setUploading(false);
    }
  };

  const removeFile = (index: number) => {
    setSelectedFiles(files => files.filter((_, i) => i !== index));
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <Box>
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleFileSelect}
        style={{ display: 'none' }}
      />
      
      <Box sx={{ mb: 2 }}>
        <Button
          variant="outlined"
          startIcon={<PhotoCamera />}
          onClick={openFileDialog}
          disabled={uploading}
        >
          {t('upload.select_files', 'Select Files')}
        </Button>
      </Box>

      {selectedFiles.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            {t('upload.selected_files', 'Selected Files')} ({selectedFiles.length})
          </Typography>
          
          <List dense>
            {selectedFiles.map((file, index) => (
              <ListItem key={index} sx={{ pl: 0 }}>
                <ListItemText
                  primary={file.name}
                  secondary={`${(file.size / 1024 / 1024).toFixed(2)} MB`}
                />
                <IconButton
                  edge="end"
                  onClick={() => removeFile(index)}
                  disabled={uploading}
                >
                  <Delete />
                </IconButton>
              </ListItem>
            ))}
          </List>

          <Button
            variant="contained"
            startIcon={<CloudUpload />}
            onClick={handleUpload}
            disabled={uploading || selectedFiles.length === 0}
            fullWidth
          >
            {uploading 
              ? t('upload.uploading', 'Uploading...') 
              : t('upload.upload', 'Upload Files')
            }
          </Button>
        </Box>
      )}
    </Box>
  );
};

// Camera capture component for direct photo taking
export const CameraCapture: React.FC<{ onCapture: (file: File) => void }> = ({ onCapture }) => {
  const { t } = useTranslation();
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isActive, setIsActive] = React.useState(false);
  const [stream, setStream] = React.useState<MediaStream | null>(null);

  const startCamera = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({ 
        video: { facingMode: 'environment' } 
      });
      setStream(mediaStream);
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
      setIsActive(true);
    } catch (error) {
      console.error('Camera access failed:', error);
      alert(t('camera.error', 'Camera access failed'));
    }
  };

  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
    setIsActive(false);
  };

  const capturePhoto = () => {
    if (!videoRef.current || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const video = videoRef.current;
    
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.drawImage(video, 0, 0);
      
      canvas.toBlob((blob) => {
        if (blob) {
          const file = new File([blob], `photo_${Date.now()}.jpg`, { type: 'image/jpeg' });
          onCapture(file);
          stopCamera();
        }
      }, 'image/jpeg', 0.8);
    }
  };

  React.useEffect(() => {
    return () => {
      stopCamera();
    };
  }, []);

  return (
    <Box>
      {!isActive ? (
        <Button variant="outlined" startIcon={<PhotoCamera />} onClick={startCamera}>
          {t('camera.start', 'Start Camera')}
        </Button>
      ) : (
        <Box>
          <video
            ref={videoRef}
            autoPlay
            playsInline
            style={{ width: '100%', maxWidth: '400px' }}
          />
          <canvas ref={canvasRef} style={{ display: 'none' }} />
          <Box sx={{ mt: 1 }}>
            <Button variant="contained" onClick={capturePhoto} sx={{ mr: 1 }}>
              {t('camera.capture', 'Capture')}
            </Button>
            <Button variant="outlined" onClick={stopCamera}>
              {t('camera.cancel', 'Cancel')}
            </Button>
          </Box>
        </Box>
      )}
    </Box>
  );
};
