{"name": "rafco-wms-desktop", "version": "0.1.0", "private": true, "description": "RAFCO Warehouse Management System - Standalone Electron + React + TypeScript", "main": "dist/main/main.js", "author": "RAFCO", "license": "UNLICENSED", "type": "module", "scripts": {"dev": "concurrently -n MAIN,RENDERER,ELECTRON -c blue,magenta,green \"npm:dev:main\" \"npm:dev:renderer\" \"npm:dev:electron\"", "dev:main": "tsup src/main/main.ts src/main/preload.ts --out-dir dist/main --format cjs --watch --sourcemap", "dev:renderer": "vite", "dev:electron": "wait-on http://localhost:5173 && electron .", "dev:server": "concurrently -n SERVER,RENDERER -c cyan,magenta \"npm:dev:server:watch\" \"npm:dev:renderer\"", "dev:server:watch": "tsup src/server/server.ts --out-dir dist/server --format cjs --watch --sourcemap && node dist/server/server.js", "build": "rimraf dist && tsup src/main/main.ts src/main/preload.ts --out-dir dist/main --format cjs && vite build", "build:server": "rimraf dist && tsup src/server/server.ts --out-dir dist/server --format cjs && vite build", "pack": "npm run build && electron-builder --dir", "portable": "npm run build && electron-builder --win portable", "portable:server": "npm run build:server && pkg dist/server/server.js --targets node18-win-x64 --output release/RAFCO-WMS-Server.exe --config pkg.config.json", "start": "electron .", "start:server": "node dist/server/server.js", "migrate": "knex --knexfile src/main/db/knexfile.cjs migrate:latest"}, "build": {"appId": "com.rafco.wms", "productName": "RAFCO WMS", "asar": true, "directories": {"output": "release"}, "files": ["dist/**/*", "assets/**/*", "package.json"], "extraResources": [{"from": "assets", "to": "assets"}, {"from": "src/main/db/migrations", "to": "db/migrations"}], "win": {"target": [{"target": "portable", "arch": ["x64"], "requestedExecutionLevel": "asInvoker"}], "artifactName": "${productName} Portable.${ext}", "icon": "assets/icon.ico"}, "portable": {"requestElevation": false, "unpackDirName": "RAFCO-WMS-Desktop"}, "publish": [{"provider": "generic", "url": "file://\\\\server\\rafco-wms\\updates"}]}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.16.6", "@mui/material": "^5.16.7", "@zxing/library": "^0.20.0", "better-sqlite3": "^9.6.0", "cors": "^2.8.5", "dayjs": "^1.11.11", "electron-log": "^5.2.0", "electron-updater": "^6.3.9", "exceljs": "^4.4.0", "express": "^4.19.2", "i18next": "^23.11.5", "jszip": "^3.10.1", "knex": "^3.1.0", "multer": "^1.4.5-lts.1", "open": "^10.1.0", "papaparse": "^5.4.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^14.1.0", "stylis": "^4.3.2", "stylis-plugin-rtl": "^2.1.1"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/multer": "^1.4.11", "@types/node": "^22.5.0", "@types/react": "^18.2.79", "@types/react-dom": "^18.2.25", "@vitejs/plugin-react": "^4.3.1", "concurrently": "^9.1.0", "electron": "^30.0.9", "electron-builder": "^24.13.3", "pkg": "^5.8.1", "rimraf": "^6.0.1", "tsup": "^8.2.4", "typescript": "^5.5.4", "vite": "^5.4.1", "wait-on": "^7.2.0"}}