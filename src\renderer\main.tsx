import React from 'react';
import ReactDOM from 'react-dom/client';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import Button from '@mui/material/Button';
import { CacheProvider } from '@emotion/react';
import createCache from '@emotion/cache';
import rtlPlugin from 'stylis-plugin-rtl';
import { prefixer } from 'stylis';
import i18n from './services/i18n';
import { useTranslation } from 'react-i18next';

const cacheLtr = createCache({ key: 'muiltr' });
const cacheRtl = createCache({ key: 'muirtl', stylisPlugins: [prefixer, rtlPlugin] });

function App() {
  const { t, i18n } = useTranslation();
  const isRtl = i18n.language === 'ar';
  const theme = React.useMemo(() => createTheme({ direction: isRtl ? 'rtl' : 'ltr' }), [isRtl]);

  React.useEffect(() => {
    document.dir = isRtl ? 'rtl' : 'ltr';
  }, [isRtl]);

  return (
    <CacheProvider value={isRtl ? cacheRtl : cacheLtr}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <div style={{ padding: 24 }}>
          <h1>{t('app.title')}</h1>
          <p>{t('app.welcome')}</p>
          <Button variant="contained" onClick={() => i18n.changeLanguage(isRtl ? 'en' : 'ar')}>
            {isRtl ? 'English' : 'العربية'}
          </Button>
        </div>
      </ThemeProvider>
    </CacheProvider>
  );
}

ReactDOM.createRoot(document.getElementById('root')!).render(<App />);

