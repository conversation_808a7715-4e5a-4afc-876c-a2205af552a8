import React from 'react';
import ReactDOM from 'react-dom/client';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import Button from '@mui/material/Button';
import Alert from '@mui/material/Alert';
import Box from '@mui/material/Box';
import { CacheProvider } from '@emotion/react';
import createCache from '@emotion/cache';
import rtlPlugin from 'stylis-plugin-rtl';
import { prefixer } from 'stylis';
import i18n from './services/i18n';
import { useTranslation } from 'react-i18next';
import { api, browserSupport } from './services/api';

const cacheLtr = createCache({ key: 'muiltr' });
const cacheRtl = createCache({ key: 'muirtl', stylisPlugins: [prefixer, rtlPlugin] });

function App() {
  const { t, i18n } = useTranslation();
  const [isConnected, setIsConnected] = React.useState(false);
  const [browserCompatibility, setBrowserCompatibility] = React.useState({
    camera: false,
    fileApi: false,
    localStorage: false
  });

  const isRtl = i18n.language === 'ar';
  const theme = React.useMemo(() => createTheme({ direction: isRtl ? 'rtl' : 'ltr' }), [isRtl]);

  React.useEffect(() => {
    document.dir = isRtl ? 'rtl' : 'ltr';

    // Check server connection
    api.ping().then(() => setIsConnected(true)).catch(() => setIsConnected(false));

    // Check browser compatibility
    setBrowserCompatibility({
      camera: browserSupport.camera(),
      fileApi: browserSupport.fileApi(),
      localStorage: browserSupport.localStorage()
    });
  }, [isRtl]);

  const testDatabase = async () => {
    try {
      const result = await api.db.query('SELECT 1 as test');
      alert('Database connection successful: ' + JSON.stringify(result));
    } catch (error) {
      alert('Database error: ' + error);
    }
  };

  return (
    <CacheProvider value={isRtl ? cacheRtl : cacheLtr}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Box sx={{ padding: 3 }}>
          <h1>{t('app.title')}</h1>
          <p>{t('app.welcome')}</p>

          {!isConnected && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {isRtl ? 'لا يمكن الاتصال بالخادم' : 'Cannot connect to server'}
            </Alert>
          )}

          <Box sx={{ mb: 2 }}>
            <Button variant="contained" onClick={() => i18n.changeLanguage(isRtl ? 'en' : 'ar')} sx={{ mr: 1 }}>
              {isRtl ? 'English' : 'العربية'}
            </Button>
            <Button variant="outlined" onClick={testDatabase} disabled={!isConnected}>
              {isRtl ? 'اختبار قاعدة البيانات' : 'Test Database'}
            </Button>
          </Box>

          <Box sx={{ mt: 2 }}>
            <h3>{isRtl ? 'حالة المتصفح' : 'Browser Status'}</h3>
            <Alert severity={browserCompatibility.camera ? 'success' : 'warning'}>
              {isRtl ? 'الكاميرا: ' : 'Camera: '} {browserCompatibility.camera ? '✓' : '✗'}
            </Alert>
            <Alert severity={browserCompatibility.fileApi ? 'success' : 'error'}>
              {isRtl ? 'ملفات API: ' : 'File API: '} {browserCompatibility.fileApi ? '✓' : '✗'}
            </Alert>
            <Alert severity={browserCompatibility.localStorage ? 'success' : 'warning'}>
              {isRtl ? 'التخزين المحلي: ' : 'Local Storage: '} {browserCompatibility.localStorage ? '✓' : '✗'}
            </Alert>
          </Box>
        </Box>
      </ThemeProvider>
    </CacheProvider>
  );
}

ReactDOM.createRoot(document.getElementById('root')!).render(<App />);

