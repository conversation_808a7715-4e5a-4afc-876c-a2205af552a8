import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema
    .createTable('users', t => { t.increments('id').primary(); t.string('username').unique().notNullable(); t.string('display_name'); t.string('password_hash'); t.boolean('active').defaultTo(true); })
    .createTable('roles', t => { t.increments('id').primary(); t.string('name').unique().notNullable(); })
    .createTable('permissions', t => { t.increments('id').primary(); t.string('name').unique().notNullable(); })
    .createTable('user_roles', t => { t.integer('user_id').references('users.id'); t.integer('role_id').references('roles.id'); t.primary(['user_id','role_id']); })
    .createTable('role_permissions', t => { t.integer('role_id').references('roles.id'); t.integer('permission_id').references('permissions.id'); t.primary(['role_id','permission_id']); })
    .createTable('projects', t => { t.increments('id').primary(); t.string('code').unique(); t.string('name_en').notNullable(); t.string('name_ar').notNullable(); t.string('customer'); t.date('start_date'); t.date('end_date'); t.decimal('budget',14,2).defaultTo(0); t.string('status').index(); })
    .createTable('warehouses', t => { t.increments('id').primary(); t.string('name_en').notNullable(); t.string('name_ar').notNullable(); t.string('code').unique().notNullable(); })
    .createTable('locations', t => { t.increments('id').primary(); t.integer('warehouse_id').references('warehouses.id'); t.string('code').notNullable(); t.string('desc'); t.unique(['warehouse_id','code']); })
    .createTable('materials', t => { t.increments('id').primary(); t.string('barcode').index(); t.string('sku').unique(); t.string('name_en').notNullable(); t.string('name_ar').notNullable(); t.string('uom').notNullable(); t.string('category'); t.decimal('min_qty',14,3).defaultTo(0); t.decimal('reorder_point',14,3).defaultTo(0); })
    .createTable('stock', t => { t.increments('id').primary(); t.integer('material_id').references('materials.id').index(); t.integer('warehouse_id').references('warehouses.id').index(); t.integer('location_id').references('locations.id'); t.decimal('qty_on_hand',14,3).defaultTo(0); t.decimal('avg_cost',14,4).defaultTo(0); t.unique(['material_id','warehouse_id','location_id']); })
    .createTable('suppliers', t => { t.increments('id').primary(); t.string('name'); t.string('contact'); })
    .createTable('purchase_orders', t => { t.increments('id').primary(); t.string('po_number').unique(); t.integer('project_id').references('projects.id'); t.integer('supplier_id').references('suppliers.id'); t.date('order_date'); t.string('status'); })
    .createTable('po_lines', t => { t.increments('id').primary(); t.integer('po_id').references('purchase_orders.id').index(); t.integer('material_id').references('materials.id'); t.decimal('qty',14,3); t.decimal('unit_price',14,4); })
    .createTable('grn', t => { t.increments('id').primary(); t.string('grn_number').unique(); t.integer('po_id').references('purchase_orders.id'); t.date('grn_date'); t.string('qc_status').index(); t.integer('warehouse_id').references('warehouses.id'); t.text('notes'); })
    .createTable('grn_lines', t => { t.increments('id').primary(); t.integer('grn_id').references('grn.id').index(); t.integer('material_id').references('materials.id'); t.decimal('qty_received',14,3); t.decimal('qty_accepted',14,3); t.integer('putaway_location_id').references('locations.id'); })
    .createTable('issue_notes', t => { t.increments('id').primary(); t.string('issue_number').unique(); t.string('type').index(); t.integer('project_id').references('projects.id'); t.integer('warehouse_id').references('warehouses.id'); t.date('issue_date'); t.text('notes'); })
    .createTable('issue_lines', t => { t.increments('id').primary(); t.integer('issue_id').references('issue_notes.id').index(); t.integer('material_id').references('materials.id'); t.decimal('qty',14,3); t.decimal('unit_cost',14,4); })
    .createTable('waste_logs', t => { t.increments('id').primary(); t.integer('warehouse_id').references('warehouses.id'); t.string('type'); t.decimal('qty',14,3); t.date('date'); t.text('notes'); })
    .createTable('manufacturing_orders', t => { t.increments('id').primary(); t.string('mo_number').unique(); t.integer('warehouse_id').references('warehouses.id'); t.date('date'); t.text('notes'); })
    .createTable('consumption_lines', t => { t.increments('id').primary(); t.integer('mo_id').references('manufacturing_orders.id'); t.integer('material_id').references('materials.id'); t.decimal('qty',14,3); t.string('cost_center'); })
    .createTable('shipments', t => { t.increments('id').primary(); t.string('bol_number').unique(); t.date('date'); t.integer('warehouse_id').references('warehouses.id'); t.string('status').index(); t.text('notes'); })
    .createTable('shipment_lines', t => { t.increments('id').primary(); t.integer('shipment_id').references('shipments.id').index(); t.integer('material_id').references('materials.id'); t.decimal('qty',14,3); t.decimal('weight',14,3); t.text('description'); })
    .createTable('attachments', t => { t.increments('id').primary(); t.string('entity_type'); t.integer('entity_id'); t.string('path'); t.string('mime'); t.dateTime('created_at').defaultTo(knex.fn.now()); })
    .createTable('audit_log', t => { t.increments('id').primary(); t.integer('user_id').references('users.id'); t.string('action'); t.string('entity_type'); t.integer('entity_id'); t.dateTime('created_at').defaultTo(knex.fn.now()); t.text('metadata'); })
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema
    .dropTableIfExists('audit_log')
    .dropTableIfExists('attachments')
    .dropTableIfExists('shipment_lines')
    .dropTableIfExists('shipments')
    .dropTableIfExists('consumption_lines')
    .dropTableIfExists('manufacturing_orders')
    .dropTableIfExists('waste_logs')
    .dropTableIfExists('issue_lines')
    .dropTableIfExists('issue_notes')
    .dropTableIfExists('grn_lines')
    .dropTableIfExists('grn')
    .dropTableIfExists('po_lines')
    .dropTableIfExists('purchase_orders')
    .dropTableIfExists('suppliers')
    .dropTableIfExists('stock')
    .dropTableIfExists('materials')
    .dropTableIfExists('locations')
    .dropTableIfExists('warehouses')
    .dropTableIfExists('projects')
    .dropTableIfExists('role_permissions')
    .dropTableIfExists('user_roles')
    .dropTableIfExists('permissions')
    .dropTableIfExists('roles')
    .dropTableIfExists('users');
}

