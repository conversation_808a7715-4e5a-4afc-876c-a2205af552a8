<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="RAFCO Warehouse Management System - Professional inventory and warehouse management solution">
    <meta name="keywords" content="warehouse, inventory, management, RAFCO, WMS, logistics">
    <meta name="author" content="RAFCO">
    
    <title>RAFCO WMS - Warehouse Management System</title>
    
    <!-- Favicon and Icons -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="assets/icon-32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/icon-16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/icon-180.png">
    <link rel="manifest" href="assets/manifest.json">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .launcher-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
            position: relative;
            overflow: hidden;
        }
        
        .launcher-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        
        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: bold;
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        h1 {
            font-size: 28px;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .subtitle {
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 16px;
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            font-weight: 500;
        }
        
        .status.loading {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }
        
        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #c8e6c9;
        }
        
        .status.error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #ffcdd2;
        }
        
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1976d2;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 20px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
        }
        
        .btn-secondary:hover {
            background: #e9ecef;
        }
        
        .language-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: none;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .language-toggle:hover {
            background: #f8f9fa;
        }
        
        /* RTL Support */
        [dir="rtl"] {
            text-align: right;
        }
        
        [dir="rtl"] .language-toggle {
            right: auto;
            left: 20px;
        }
        
        [dir="rtl"] .btn {
            flex-direction: row-reverse;
        }
        
        .version-info {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #999;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="launcher-container">
        <button class="language-toggle" onclick="toggleLanguage()">
            <span id="lang-toggle-text">العربية</span>
        </button>
        
        <div class="logo">
            <span>📦</span>
        </div>
        
        <h1 id="title">RAFCO WMS</h1>
        <p class="subtitle" id="subtitle">Warehouse Management System</p>
        
        <div id="status" class="status loading">
            <div class="spinner"></div>
            <span id="status-text">Checking application status...</span>
        </div>
        
        <div id="buttons" class="buttons hidden">
            <a href="#" id="launch-btn" class="btn btn-primary" onclick="launchApp()">
                <span>🚀</span>
                <span id="launch-text">Launch Application</span>
            </a>
            <button class="btn btn-secondary" onclick="refreshStatus()">
                <span>🔄</span>
                <span id="refresh-text">Refresh</span>
            </button>
        </div>
        
        <div class="version-info">
            <div id="version-text">Version 1.0.0 | RAFCO Company</div>
        </div>
    </div>

    <script>
        let currentLang = 'en';
        let appUrl = null;
        
        const translations = {
            en: {
                title: 'RAFCO WMS',
                subtitle: 'Warehouse Management System',
                langToggle: 'العربية',
                statusChecking: 'Checking application status...',
                statusReady: 'Application is ready!',
                statusError: 'Application server is not running',
                statusStarting: 'Please start the RAFCO WMS Server first',
                launchText: 'Launch Application',
                refreshText: 'Refresh',
                versionText: 'Version 1.0.0 | RAFCO Company',
                instructions: 'Double-click RAFCO-WMS-Server.exe to start the server, then refresh this page.'
            },
            ar: {
                title: 'نظام مستودعات RAFCO',
                subtitle: 'نظام إدارة المستودعات',
                langToggle: 'English',
                statusChecking: 'فحص حالة التطبيق...',
                statusReady: 'التطبيق جاهز للاستخدام!',
                statusError: 'خادم التطبيق غير يعمل',
                statusStarting: 'يرجى تشغيل خادم RAFCO WMS أولاً',
                launchText: 'تشغيل التطبيق',
                refreshText: 'تحديث',
                versionText: 'الإصدار 1.0.0 | شركة رافكو',
                instructions: 'انقر نقراً مزدوجاً على RAFCO-WMS-Server.exe لبدء الخادم، ثم حدث هذه الصفحة.'
            }
        };
        
        function updateLanguage() {
            const t = translations[currentLang];
            document.documentElement.lang = currentLang;
            document.documentElement.dir = currentLang === 'ar' ? 'rtl' : 'ltr';
            
            document.getElementById('title').textContent = t.title;
            document.getElementById('subtitle').textContent = t.subtitle;
            document.getElementById('lang-toggle-text').textContent = t.langToggle;
            document.getElementById('launch-text').textContent = t.launchText;
            document.getElementById('refresh-text').textContent = t.refreshText;
            document.getElementById('version-text').textContent = t.versionText;
        }
        
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'ar' : 'en';
            updateLanguage();
            localStorage.setItem('rafco-wms-lang', currentLang);
        }
        
        async function checkAppStatus() {
            const statusEl = document.getElementById('status');
            const statusTextEl = document.getElementById('status-text');
            const buttonsEl = document.getElementById('buttons');
            const t = translations[currentLang];
            
            // Reset status
            statusEl.className = 'status loading';
            statusTextEl.textContent = t.statusChecking;
            buttonsEl.classList.add('hidden');
            
            try {
                // Check if server is running
                const response = await fetch('http://localhost:3000/health', {
                    method: 'GET',
                    timeout: 5000
                });
                
                if (response.ok) {
                    appUrl = 'http://localhost:3000';
                    statusEl.className = 'status success';
                    statusTextEl.textContent = t.statusReady;
                    buttonsEl.classList.remove('hidden');
                } else {
                    throw new Error('Server not responding');
                }
            } catch (error) {
                // Try alternative ports
                const ports = [3001, 3002, 3003, 3004, 3005];
                let found = false;
                
                for (const port of ports) {
                    try {
                        const response = await fetch(`http://localhost:${port}/health`, {
                            method: 'GET',
                            timeout: 2000
                        });
                        if (response.ok) {
                            appUrl = `http://localhost:${port}`;
                            statusEl.className = 'status success';
                            statusTextEl.textContent = t.statusReady;
                            buttonsEl.classList.remove('hidden');
                            found = true;
                            break;
                        }
                    } catch (e) {
                        // Continue to next port
                    }
                }
                
                if (!found) {
                    statusEl.className = 'status error';
                    statusTextEl.innerHTML = `
                        <div>${t.statusError}</div>
                        <div style="font-size: 12px; margin-top: 8px; opacity: 0.8;">
                            ${t.instructions}
                        </div>
                    `;
                    buttonsEl.classList.remove('hidden');
                }
            }
        }
        
        function launchApp() {
            if (appUrl) {
                window.open(appUrl, '_blank');
            } else {
                alert(translations[currentLang].statusStarting);
            }
        }
        
        function refreshStatus() {
            checkAppStatus();
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Load saved language preference
            const savedLang = localStorage.getItem('rafco-wms-lang');
            if (savedLang && translations[savedLang]) {
                currentLang = savedLang;
            }
            
            updateLanguage();
            checkAppStatus();
            
            // Auto-refresh every 30 seconds
            setInterval(checkAppStatus, 30000);
        });
    </script>
</body>
</html>
