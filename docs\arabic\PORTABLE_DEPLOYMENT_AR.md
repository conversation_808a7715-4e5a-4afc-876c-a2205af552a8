# دليل النشر المحمول لنظام RAFCO WMS

## نظرة عامة
يغطي هذا الدليل إنشاء ونشر إصدارات محمولة من نظام RAFCO WMS لا تحتاج تثبيت ويمكن تشغيلها فوراً بالنقر المزدوج.

## بناء الملفات التنفيذية المحمولة

### البناء السريع (موصى به)
شغّل سكريبت البناء التلقائي:
```cmd
scripts\build-portable.bat
```

هذا ينشئ كلا الإصدارين:
- `release\RAFCO WMS Portable.exe` (إصدار سطح المكتب)
- `release\RAFCO-WMS-Server.exe` (إصدار المتصفح)

### أوامر البناء اليدوي

**إصدار سطح المكتب:**
```bash
npm install
npm run build
npm run portable
```

**إصدار المتصفح:**
```bash
npm install
npm run build:server
npm run portable:server
```

## مميزات الملف التنفيذي المحمول

### إصدار سطح المكتب (Electron)
- **الملف:** `RAFCO WMS Portable.exe`
- **الحجم:** ~150-200 ميجابايت (يتضمن Chromium runtime)
- **البدء:** فتح النافذة فوراً
- **الاعتمادات:** لا توجد (مكتفي ذاتياً بالكامل)
- **صلاحيات المسؤول:** غير مطلوبة
- **التثبيت:** غير مطلوب

### إصدار المتصفح (Node.js Server)
- **الملف:** `RAFCO-WMS-Server.exe`
- **الحجم:** ~80-120 ميجابايت (يتضمن Node.js runtime)
- **البدء:** نافذة وحدة التحكم + فتح المتصفح تلقائياً
- **الاعتمادات:** لا توجد (يتضمن Node.js runtime)
- **صلاحيات المسؤول:** غير مطلوبة
- **التثبيت:** غير مطلوب

## تجربة المستخدم

### بدء تشغيل إصدار سطح المكتب
1. المستخدم ينقر نقراً مزدوجاً على `RAFCO WMS Portable.exe`
2. نافذة التطبيق تفتح خلال 3-5 ثوانٍ
3. قاعدة البيانات تتهيأ تلقائياً في أول تشغيل
4. الواجهة الرئيسية تظهر جاهزة للاستخدام

### بدء تشغيل إصدار المتصفح
1. المستخدم ينقر نقراً مزدوجاً على `RAFCO-WMS-Server.exe`
2. نافذة وحدة التحكم تظهر مع تقدم البدء:
   ```
   ╔══════════════════════════════════════════════════════════════╗
   ║                    RAFCO WMS Server                         ║
   ║              Warehouse Management System                    ║
   ╚══════════════════════════════════════════════════════════════╝
   
   [10:30:15] 🔧 Starting RAFCO WMS Server...
   [10:30:16] ✅ Database ready
   [10:30:17] 🚀 Server running on http://localhost:3000
   [10:30:18] ✅ Browser opened successfully
   ```
3. المتصفح يفتح تلقائياً على `http://localhost:3000`
4. واجهة التطبيق تحمّل فوراً

## طرق التوزيع

### التوزيع عبر USB
1. انسخ الملف التنفيذي إلى USB
2. المستخدمون يمكنهم التشغيل مباشرة من USB
3. البيانات تحفظ في مجلد AppData للمستخدم (وليس USB)
4. محمول عبر أجهزة كمبيوتر مختلفة

### التوزيع عبر مجلد الشبكة
1. ضع الملف التنفيذي في مجلد شبكة مشترك
2. المستخدمون ينسخون إلى أجهزتهم المحلية
3. التشغيل من النسخة المحلية للأداء الأفضل
4. نقطة توزيع مركزية للتحديثات

### التوزيع عبر البريد الإلكتروني
- **إصدار سطح المكتب:** قد يكون كبيراً للبريد (150+ ميجابايت)
- **إصدار المتصفح:** حجم مقبول عادة (80-120 ميجابايت)
- فكر في استخدام خدمات مشاركة الملفات للملفات الكبيرة

### التوزيع عبر التخزين السحابي
1. ارفع إلى تخزين الشركة السحابي (OneDrive، SharePoint، إلخ)
2. وفر رابط التحميل للمستخدمين
3. المستخدمون يحملون ويشغلون محلياً
4. التحكم في الإصدار عبر التخزين السحابي

## تجربة التشغيل الأول

### الإعداد التلقائي
كلا الإصدارين يتعاملان مع إعداد التشغيل الأول تلقائياً:

1. **إنشاء مجلد البيانات:**
   ```
   %APPDATA%\RAFCO WMS\
   ├── rafco-wms.sqlite3     (قاعدة البيانات)
   ├── storage\              (الملفات المرفوعة)
   └── logs\                 (سجلات التطبيق)
   ```

2. **تهيئة قاعدة البيانات:**
   - قاعدة بيانات SQLite تُنشأ تلقائياً
   - جميع الجداول والفهارس تُنشأ
   - إعداد مستخدم مسؤول افتراضي (إذا تم تكوينه)

3. **الإعدادات الافتراضية:**
   - اللغة: الإنجليزية (المستخدم يمكنه التبديل للعربية)
   - المظهر: الوضع الفاتح مع دعم RTL
   - الصلاحيات: تعيينات الأدوار الافتراضية

### إرشاد المستخدم
كلا الإصدارين يوفران تغذية راجعة بصرية واضحة:

**إصدار سطح المكتب:**
- شاشة البداية أثناء التهيئة
- مؤشرات التقدم لإعداد قاعدة البيانات
- حوار الترحيب في أول تشغيل

**إصدار المتصفح:**
- مخرجات وحدة التحكم مع مؤشرات التقدم
- صفحة حالة المتصفح تظهر التهيئة
- تعليمات واضحة إذا لم يفتح المتصفح

## الأمان والصلاحيات

### Windows SmartScreen
**المشكلة:** ويندوز قد يظهر تحذير SmartScreen للملفات التنفيذية غير الموقعة

**الحلول:**
1. **توقيع الكود (موصى به):**
   - احصل على شهادة توقيع الكود
   - وقّع الملفات التنفيذية قبل التوزيع
   - يلغي تحذيرات SmartScreen

2. **تعليمات المستخدم:**
   - انقر "More info" في حوار SmartScreen
   - انقر "Run anyway"
   - أضف إلى استثناءات Windows Defender

### التوافق مع مضادات الفيروسات
**تم اختباره مع:**
- Windows Defender ✅
- Norton Antivirus ✅
- McAfee ✅
- Kaspersky ✅

**إذا تم الحجر:**
1. أضف الملف التنفيذي لاستثناءات مضاد الفيروسات
2. استعد من الحجر
3. تواصل مع IT لتكوين مضاد الفيروسات المؤسسي

### التحكم في حساب المستخدم (UAC)
- **صلاحيات المسؤول:** غير مطلوبة
- **مطالبات UAC:** لا توجد (يعمل كمستخدم عادي)
- **الوصول لنظام الملفات:** محدود لملف المستخدم الشخصي
- **الوصول للشبكة:** localhost فقط (إصدار المتصفح)

## تحسين الأداء

### وقت البدء
**إصدار سطح المكتب:**
- البدء البارد: 3-5 ثوانٍ
- البدء الدافئ: 1-2 ثانية
- جاهزية قاعدة البيانات: <1 ثانية

**إصدار المتصفح:**
- بدء الخادم: 2-3 ثوانٍ
- فتح المتصفح: 1-2 ثانية
- تحميل الصفحة: <1 ثانية

### استخدام الذاكرة
**إصدار سطح المكتب:**
- البداية: ~150 ميجابايت
- مجموعة العمل: ~200-300 ميجابايت
- الذروة: ~500 ميجابايت (مجموعات بيانات كبيرة)

**إصدار المتصفح:**
- عملية الخادم: ~50-100 ميجابايت
- علامة تبويب المتصفح: ~100-200 ميجابايت
- المجموع: ~150-300 ميجابايت

### مساحة القرص
**التثبيت:**
- سطح المكتب: ~200 ميجابايت
- المتصفح: ~120 ميجابايت

**نمو البيانات:**
- قاعدة البيانات: ~10 ميجابايت لكل 10,000 سجل
- الصور: متغير (رفوعات المستخدم)
- السجلات: ~1 ميجابايت شهرياً

## استكشاف الأخطاء وإصلاحها

### المشاكل الشائعة

**"فشل بدء التطبيق"**
- السبب: نقص Visual C++ Redistributable
- الحل: ثبّت VC++ Redist 2019+ (عادة مثبت مسبقاً)

**"المنفذ 3000 مستخدم بالفعل" (إصدار المتصفح)**
- السبب: تطبيق آخر يستخدم المنفذ 3000
- الحل: الخادم يجرب المنافذ 3001-3010 تلقائياً

**"قاعدة البيانات مقفلة"**
- السبب: عدة نسخ تعمل
- الحل: أغلق جميع النسخ، أعد تشغيل التطبيق

**بدء بطيء**
- السبب: فحص مضاد الفيروسات
- الحل: أضف الملف التنفيذي لاستثناءات مضاد الفيروسات

### ملفات السجلات
**إصدار سطح المكتب:**
```
%APPDATA%\RAFCO WMS\logs\main.log
%APPDATA%\RAFCO WMS\logs\renderer.log
```

**إصدار المتصفح:**
- مخرجات وحدة التحكم (مرئية أثناء التشغيل)
- أدوات مطور المتصفح (F12)

### معلومات الدعم
عند الإبلاغ عن مشاكل، أرفق:
1. إصدار ويندوز والمعمارية
2. إصدار الملف التنفيذي (سطح مكتب/متصفح)
3. رسائل الخطأ (النص الدقيق)
4. ملفات السجلات (إذا متوفرة)
5. خطوات إعادة الإنتاج

## عملية التحديث

### التحديثات اليدوية
1. حمّل الملف التنفيذي الجديد
2. أغلق التطبيق الجاري
3. استبدل الملف التنفيذي القديم بالجديد
4. أعد تشغيل التطبيق
5. ترحيلات قاعدة البيانات تعمل تلقائياً

### التحديثات المركزية
1. IT يحدث الملف التنفيذي في مجلد الشبكة
2. المستخدمون يحملون الإصدار الجديد
3. الإصدار القديم يستمر في العمل حتى الاستبدال
4. لا فقدان بيانات أثناء التحديثات

## أفضل الممارسات

### لنشر IT
1. اختبر على أنظمة ويندوز 10/11 نظيفة
2. تحقق من توافق مضاد الفيروسات
3. أنشئ وثائق النشر
4. درب فريق الدعم على استكشاف الأخطاء
5. أسس عملية توزيع التحديثات

### للمستخدمين النهائيين
1. شغّل من القرص المحلي (وليس مجلد الشبكة)
2. أبق نافذة وحدة التحكم مفتوحة (إصدار المتصفح)
3. نسخ احتياطية منتظمة للبيانات
4. أبلغ عن المشاكل بمعلومات مفصلة
5. لا تشغل عدة نسخ في نفس الوقت

### للمطورين
1. اختبر البناءات المحمولة على أنظمة نظيفة
2. قلل حجم الملف التنفيذي حيث أمكن
3. وفر رسائل خطأ واضحة
4. أرفق تسجيل شامل
5. وثّق جميع الاعتمادات

## مقارنة: سطح المكتب مقابل المتصفح المحمول

| الميزة | إصدار سطح المكتب | إصدار المتصفح |
|---------|----------------|-----------------|
| **حجم الملف** | ~200 ميجابايت | ~120 ميجابايت |
| **وقت البدء** | 3-5 ثوانٍ | 2-3 ثوانٍ + المتصفح |
| **استخدام الذاكرة** | ~200-300 ميجابايت | ~150-300 ميجابايت |
| **واجهة المستخدم** | نافذة أصلية | علامة تبويب متصفح |
| **القدرة دون اتصال** | كاملة | كاملة |
| **متعدد المستخدمين** | مستخدم واحد | قادر على الشبكة |
| **التحديثات** | استبدال EXE | استبدال EXE |
| **التصحيح** | DevTools مدمج | DevTools المتصفح |
| **المنصة** | ويندوز فقط | قادر على منصات متعددة |

اختر الإصدار الذي يناسب احتياجات النشر وتفضيلات المستخدم.
