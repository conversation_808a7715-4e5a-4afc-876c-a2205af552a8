const fs = require('fs');
const path = require('path');

// This script generates icon files from the SVG
// In a real implementation, you would use a tool like sharp or imagemagick
// For now, this creates placeholder files with the correct names

const iconSizes = [16, 32, 64, 128, 180, 256, 512];
const assetsDir = path.join(__dirname, '..', 'assets');

// Ensure assets directory exists
if (!fs.existsSync(assetsDir)) {
  fs.mkdirSync(assetsDir, { recursive: true });
}

console.log('🎨 Generating RAFCO WMS Icons...');

// Generate PNG placeholders
iconSizes.forEach(size => {
  const filename = `icon-${size}.png`;
  const filepath = path.join(assetsDir, filename);
  
  // Create a simple placeholder file
  // In production, use: sharp(svgBuffer).resize(size, size).png().toFile(filepath)
  const placeholder = `# PNG Icon Placeholder ${size}x${size}
# Replace this with actual PNG file generated from icon.svg
# Recommended tools: sharp, imagemagick, or online SVG to PNG converter
# Command example: convert icon.svg -resize ${size}x${size} icon-${size}.png`;
  
  fs.writeFileSync(filepath, placeholder);
  console.log(`✅ Created ${filename} (${size}x${size})`);
});

// Generate favicon.ico placeholder
const faviconPath = path.join(assetsDir, 'favicon.ico');
const faviconPlaceholder = `# ICO Favicon Placeholder
# Replace this with actual .ico file containing multiple sizes (16x16, 32x32, 48x48)
# Recommended tools: ImageMagick, GIMP, or online ICO converter
# Command example: convert icon.svg -define icon:auto-resize=16,32,48 favicon.ico`;

fs.writeFileSync(faviconPath, faviconPlaceholder);
console.log('✅ Created favicon.ico placeholder');

// Update package.json icon reference
const packageJsonPath = path.join(__dirname, '..', 'package.json');
if (fs.existsSync(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Update electron-builder icon configuration
  if (packageJson.build && packageJson.build.win) {
    packageJson.build.win.icon = 'assets/icon-256.png';
    console.log('✅ Updated package.json with icon reference');
  }
  
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
}

console.log('');
console.log('📋 Next Steps:');
console.log('1. Install imagemagick or sharp: npm install sharp');
console.log('2. Convert SVG to PNG files:');
console.log('   - Use online converter: https://convertio.co/svg-png/');
console.log('   - Or use ImageMagick: convert assets/icon.svg -resize 256x256 assets/icon-256.png');
console.log('3. Generate favicon.ico: convert assets/icon.svg -define icon:auto-resize=16,32,48 assets/favicon.ico');
console.log('4. Replace placeholder files with actual images');
console.log('');
console.log('🎯 Icon Design Guidelines:');
console.log('- Use the SVG as the master design');
console.log('- Ensure icons are crisp at all sizes');
console.log('- Test visibility on light and dark backgrounds');
console.log('- Maintain brand consistency with RAFCO colors');
