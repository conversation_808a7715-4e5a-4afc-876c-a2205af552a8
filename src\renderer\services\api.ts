// Browser-compatible API client to replace Electron IPC

const API_BASE = '/api';

export const api = {
  ping: async () => {
    const response = await fetch(`${API_BASE}/ping`);
    return response.json();
  },

  db: {
    query: async (sql: string, params?: any[]) => {
      const response = await fetch(`${API_BASE}/db/query`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sql, params })
      });
      const result = await response.json();
      if (!result.success) throw new Error(result.error);
      return result.data;
    },

    run: async (sql: string, params?: any[]) => {
      const response = await fetch(`${API_BASE}/db/run`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sql, params })
      });
      const result = await response.json();
      if (!result.success) throw new Error(result.error);
      return result.data;
    }
  },

  files: {
    uploadPhotos: async (files: FileList) => {
      const formData = new FormData();
      Array.from(files).forEach(file => {
        formData.append('photos', file);
      });
      
      const response = await fetch(`${API_BASE}/files/upload`, {
        method: 'POST',
        body: formData
      });
      const result = await response.json();
      if (!result.success) throw new Error(result.error);
      return result.data;
    },

    exportCSV: async (name: string, csv: string) => {
      const response = await fetch(`${API_BASE}/files/export/csv`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name, csv })
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${name}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        return true;
      }
      return false;
    },

    exportExcel: async (name: string, data: any[], headers?: string[]) => {
      const response = await fetch(`${API_BASE}/files/export/excel`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name, data, headers })
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${name}.xlsx`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        return true;
      }
      return false;
    }
  },

  i18n: {
    getLocale: async () => {
      const response = await fetch(`${API_BASE}/settings/locale`);
      const result = await response.json();
      if (!result.success) throw new Error(result.error);
      return result.data;
    },

    setLocale: async (locale: string) => {
      const response = await fetch(`${API_BASE}/settings/locale`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ locale })
      });
      const result = await response.json();
      if (!result.success) throw new Error(result.error);
      return result.data;
    }
  }
};

// Browser compatibility check
export const browserSupport = {
  camera: () => navigator.mediaDevices && navigator.mediaDevices.getUserMedia,
  fileApi: () => window.File && window.FileReader && window.FileList && window.Blob,
  localStorage: () => typeof Storage !== 'undefined'
};
