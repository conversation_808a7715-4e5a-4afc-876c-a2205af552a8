import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

const resources = {
  en: { translation: { 'app.title': 'RAFCO WMS', 'app.welcome': 'Welcome to the RAFCO Warehouse Management System' } },
  ar: { translation: { 'app.title': 'نظام مستودعات RAFCO', 'app.welcome': 'مرحباً بك في نظام إدارة المستودعات لرافكو' } }
};

i18n.use(initReactI18next).init({
  resources,
  lng: 'en',
  fallbackLng: 'en',
  interpolation: { escapeValue: false },
});

export default i18n;

