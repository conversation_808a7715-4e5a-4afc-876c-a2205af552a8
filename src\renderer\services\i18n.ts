import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

const resources = {
  en: {
    translation: {
      'app.title': 'RAFCO WMS',
      'app.welcome': 'Welcome to the RAFCO Warehouse Management System',
      'app.browser_version': 'Browser Version',
      'app.server_status': 'Server Status',
      'app.connected': 'Connected',
      'app.disconnected': 'Disconnected',
      'app.test_database': 'Test Database',
      'app.browser_status': 'Browser Status',
      'app.camera': 'Camera',
      'app.file_api': 'File API',
      'app.local_storage': 'Local Storage'
    }
  },
  ar: {
    translation: {
      'app.title': 'نظام مستودعات RAFCO',
      'app.welcome': 'مرحباً بك في نظام إدارة المستودعات لرافكو',
      'app.browser_version': 'إصدار المتصفح',
      'app.server_status': 'حالة الخادم',
      'app.connected': 'متصل',
      'app.disconnected': 'غير متصل',
      'app.test_database': 'اختبار قاعدة البيانات',
      'app.browser_status': 'حالة المتصفح',
      'app.camera': 'الكاميرا',
      'app.file_api': 'ملفات API',
      'app.local_storage': 'التخزين المحلي'
    }
  }
};

i18n.use(initReactI18next).init({
  resources,
  lng: 'en',
  fallbackLng: 'en',
  interpolation: { escapeValue: false },
});

export default i18n;

