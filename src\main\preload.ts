import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

contextBridge.exposeInMainWorld('rafco', {
  ping: () => ipcRenderer.invoke('ping'),
  db: {
    query: (sql: string, params?: any[]) => ipcRenderer.invoke('db:query', { sql, params }),
    run: (sql: string, params?: any[]) => ipcRenderer.invoke('db:run', { sql, params }),
  },
  files: {
    choosePhotos: () => ipcRenderer.invoke('files:choosePhotos'),
    exportCSV: (name: string, csv: string) => ipcRenderer.invoke('files:exportCSV', { name, csv }),
  },
  i18n: {
    getLocale: () => ipcRenderer.invoke('i18n:getLocale'),
    setLocale: (locale: string) => ipcRenderer.invoke('i18n:setLocale', { locale }),
  }
});

export {};
