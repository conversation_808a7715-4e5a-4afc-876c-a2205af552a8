import React, { useRef, useEffect, useState } from 'react';
import { BrowserMultiFormatReader } from '@zxing/library';
import { Button, Box, Alert, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { useTranslation } from 'react-i18next';

interface BarcodeScannerProps {
  onScan: (result: string) => void;
  onError?: (error: string) => void;
}

export const BarcodeScanner: React.FC<BarcodeScannerProps> = ({ onScan, onError }) => {
  const { t } = useTranslation();
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [reader, setReader] = useState<BrowserMultiFormatReader | null>(null);

  useEffect(() => {
    const codeReader = new BrowserMultiFormatReader();
    setReader(codeReader);
    
    return () => {
      codeReader.reset();
    };
  }, []);

  const startScanning = async () => {
    if (!reader || !videoRef.current) return;

    try {
      setError(null);
      setIsScanning(true);

      // Check for camera permission
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera not supported in this browser');
      }

      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { facingMode: 'environment' } 
      });
      
      videoRef.current.srcObject = stream;
      
      reader.decodeFromVideoDevice(undefined, videoRef.current, (result, error) => {
        if (result) {
          onScan(result.getText());
          stopScanning();
        }
        if (error && error.name !== 'NotFoundException') {
          console.warn('Scan error:', error);
        }
      });

    } catch (err: any) {
      const errorMsg = err.message || 'Failed to access camera';
      setError(errorMsg);
      onError?.(errorMsg);
      setIsScanning(false);
    }
  };

  const stopScanning = () => {
    if (reader) {
      reader.reset();
    }
    
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
    
    setIsScanning(false);
  };

  return (
    <Dialog open={isScanning} onClose={stopScanning} maxWidth="sm" fullWidth>
      <DialogTitle>
        {t('scanner.title', 'Barcode Scanner')}
      </DialogTitle>
      <DialogContent>
        <Box sx={{ textAlign: 'center' }}>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          <video
            ref={videoRef}
            style={{
              width: '100%',
              maxWidth: '400px',
              height: '300px',
              backgroundColor: '#000'
            }}
            autoPlay
            playsInline
          />
          
          <Box sx={{ mt: 2 }}>
            <Button variant="contained" onClick={startScanning} disabled={isScanning}>
              {t('scanner.start', 'Start Scanning')}
            </Button>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={stopScanning}>
          {t('common.close', 'Close')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Hook for easy barcode scanning
export const useBarcodeScanner = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [lastResult, setLastResult] = useState<string | null>(null);

  const openScanner = () => setIsOpen(true);
  const closeScanner = () => setIsOpen(false);

  const handleScan = (result: string) => {
    setLastResult(result);
    closeScanner();
  };

  const ScannerComponent = () => (
    isOpen ? (
      <BarcodeScanner 
        onScan={handleScan}
        onError={(error) => {
          console.error('Scanner error:', error);
          closeScanner();
        }}
      />
    ) : null
  );

  return {
    openScanner,
    closeScanner,
    lastResult,
    ScannerComponent
  };
};
